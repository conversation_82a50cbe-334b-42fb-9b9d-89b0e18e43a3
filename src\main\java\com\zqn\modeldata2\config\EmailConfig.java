package com.zqn.modeldata2.config;

import com.zqn.modeldata2.service.EmailPushConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件配置类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Configuration
public class EmailConfig {

    @Resource
    private EmailPushConfigService emailPushConfigService;

    /**
     * 配置JavaMailSender
     * 从数据库动态获取SMTP配置
     */
    @Bean
    public JavaMailSender javaMailSender() {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        
        try {
            // 从数据库获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            
            // 设置SMTP服务器配置
            mailSender.setHost(smtpConfig.getOrDefault("host", "smtp.gmail.com"));
            mailSender.setPort(Integer.parseInt(smtpConfig.getOrDefault("port", "587")));
            mailSender.setUsername(smtpConfig.getOrDefault("username", ""));
            mailSender.setPassword(smtpConfig.getOrDefault("password", ""));
            
            // 设置邮件属性
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", smtpConfig.getOrDefault("auth", "true"));
            props.put("mail.smtp.starttls.enable", smtpConfig.getOrDefault("starttls", "true"));
            props.put("mail.smtp.ssl.enable", smtpConfig.getOrDefault("ssl", "false"));
            props.put("mail.debug", "false"); // 可以设置为true来调试邮件发送
            
            // SSL配置
            if ("true".equals(smtpConfig.get("ssl"))) {
                props.put("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
                props.put("mail.smtp.socketFactory.port", smtpConfig.getOrDefault("port", "587"));
                props.put("mail.smtp.socketFactory.fallback", "false");
            }
            
            log.info("JavaMailSender配置完成，SMTP主机: {}, 端口: {}", 
                    mailSender.getHost(), mailSender.getPort());
                    
        } catch (Exception e) {
            log.error("配置JavaMailSender失败，使用默认配置", e);
            
            // 使用默认配置
            mailSender.setHost("smtp.gmail.com");
            mailSender.setPort(587);
            
            Properties props = mailSender.getJavaMailProperties();
            props.put("mail.transport.protocol", "smtp");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.debug", "false");
        }
        
        return mailSender;
    }
}
