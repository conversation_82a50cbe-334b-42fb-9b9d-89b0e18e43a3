package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.entity.email.EmailPushLog;
import com.zqn.modeldata2.service.EmailPushConfigService;
import com.zqn.modeldata2.service.EmailSendService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 邮件发送Service实现类
 * @date 2025/01/22 10:00
 */
@Slf4j
@Service
public class EmailSendServiceImpl implements EmailSendService {

    @Resource
    private EmailPushConfigService emailPushConfigService;

    @Resource
    private JavaMailSender javaMailSender;

    /**
     * 邮箱地址正则表达式
     */
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);

    @Override
    public boolean sendSingleEmail(EmailPushLog emailLog) {
        try {
            if (emailLog == null) {
                log.error("邮件日志为空");
                return false;
            }

            // 验证邮箱地址
            if (!isValidEmail(emailLog.getToEmail())) {
                log.error("收件人邮箱地址无效: {}", emailLog.getToEmail());
                return false;
            }

            // 创建邮件消息
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            String fromEmail = StringUtils.hasText(emailLog.getFromEmail()) ?
                    emailLog.getFromEmail() : smtpConfig.getOrDefault("username", "<EMAIL>");

            // 设置邮件信息
            helper.setFrom(fromEmail);
            helper.setTo(emailLog.getToEmail());
            helper.setSubject(emailLog.getSubject());
            helper.setText(emailLog.getContent(), true); // true表示HTML格式

            // 设置抄送
            if (StringUtils.hasText(emailLog.getCcEmail())) {
                String[] ccEmails = emailLog.getCcEmail().split(",");
                helper.setCc(ccEmails);
            }

            // 设置密送
            if (StringUtils.hasText(emailLog.getBccEmail())) {
                String[] bccEmails = emailLog.getBccEmail().split(",");
                helper.setBcc(bccEmails);
            }

            // 发送邮件
            javaMailSender.send(message);

            log.info("邮件发送成功: {} -> {}", fromEmail, emailLog.getToEmail());
            return true;

        } catch (Exception e) {
            log.error("发送邮件失败: {} -> {}", emailLog.getFromEmail(), emailLog.getToEmail(), e);
            return false;
        }
    }

    @Override
    public boolean sendTestEmail(String toEmail, String subject, String content) {
        try {
            if (!isValidEmail(toEmail)) {
                log.error("测试邮箱地址无效: {}", toEmail);
                return false;
            }

            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            String fromEmail = smtpConfig.getOrDefault("username", "<EMAIL>");

            // 创建邮件消息
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 设置邮件信息
            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(StringUtils.hasText(subject) ? subject : "邮件配置测试");
            helper.setText(StringUtils.hasText(content) ? content : "这是一封测试邮件，用于验证邮件配置是否正确。", true);

            // 发送邮件
            javaMailSender.send(message);

            log.info("测试邮件发送成功: {}", toEmail);
            return true;

        } catch (Exception e) {
            log.error("发送测试邮件失败: {}", toEmail, e);
            return false;
        }
    }

    @Override
    public boolean testSmtpConnection() {
        try {
            // 获取SMTP配置
            Map<String, String> smtpConfig = emailPushConfigService.getSmtpConfig();
            if (!isSmtpConfigValid(smtpConfig)) {
                log.error("SMTP配置无效");
                return false;
            }

            // 使用JavaMailSender测试连接
            // 创建一个简单的测试消息但不发送
            MimeMessage message = javaMailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, "UTF-8");

            helper.setFrom(smtpConfig.getOrDefault("username", "<EMAIL>"));
            helper.setTo("<EMAIL>");
            helper.setSubject("SMTP连接测试");
            helper.setText("这是SMTP连接测试", false);

            log.info("SMTP连接测试成功");
            return true;

        } catch (Exception e) {
            log.error("SMTP连接测试失败", e);
            return false;
        }
    }

    @Override
    public boolean isValidEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return pattern.matcher(email).matches();
    }

    /**
     * 验证SMTP配置是否有效
     */
    private boolean isSmtpConfigValid(Map<String, String> smtpConfig) {
        if (smtpConfig == null || smtpConfig.isEmpty()) {
            return false;
        }
        
        return StringUtils.hasText(smtpConfig.get("host")) &&
               StringUtils.hasText(smtpConfig.get("port")) &&
               StringUtils.hasText(smtpConfig.get("username")) &&
               StringUtils.hasText(smtpConfig.get("password"));
    }


}
